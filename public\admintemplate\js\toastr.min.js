/**
 * Minified by jsDelivr using Terser v5.19.2.
 * Original file: /npm/toastr@2.1.4/toastr.js
 *
 * Do NOT use SRI with dynamically generated files! More information: https://www.jsdelivr.com/using-sri-with-dynamic-files
 */
!function(e){e(["jquery"],(function(e){return function(){var t,n,s,o=0,i="error",a="info",r="success",l="warning",c={clear:function(n,s){var o=f();t||d(o);u(n,o,s)||function(n){for(var s=t.children(),o=s.length-1;o>=0;o--)u(e(s[o]),n)}(o)},remove:function(n){var s=f();t||d(s);if(n&&0===e(":focus",n).length)return void g(n);t.children().length&&t.remove()},error:function(e,t,n){return m({type:i,iconClass:f().iconClasses.error,message:e,optionsOverride:n,title:t})},getContainer:d,info:function(e,t,n){return m({type:a,iconClass:f().iconClasses.info,message:e,optionsOverride:n,title:t})},options:{},subscribe:function(e){n=e},success:function(e,t,n){return m({type:r,iconClass:f().iconClasses.success,message:e,optionsOverride:n,title:t})},version:"2.1.4",warning:function(e,t,n){return m({type:l,iconClass:f().iconClasses.warning,message:e,optionsOverride:n,title:t})}};return c;function d(n,s){return n||(n=f()),(t=e("#"+n.containerId)).length||s&&(t=function(n){return t=e("<div/>").attr("id",n.containerId).addClass(n.positionClass),t.appendTo(e(n.target)),t}(n)),t}function u(t,n,s){var o=!(!s||!s.force)&&s.force;return!(!t||!o&&0!==e(":focus",t).length)&&(t[n.hideMethod]({duration:n.hideDuration,easing:n.hideEasing,complete:function(){g(t)}}),!0)}function p(e){n&&n(e)}function m(n){var i=f(),a=n.iconClass||i.iconClass;if(void 0!==n.optionsOverride&&(i=e.extend(i,n.optionsOverride),a=n.optionsOverride.iconClass||a),!function(e,t){if(e.preventDuplicates){if(t.message===s)return!0;s=t.message}return!1}(i,n)){o++,t=d(i,!0);var r=null,l=e("<div/>"),c=e("<div/>"),u=e("<div/>"),m=e("<div/>"),h=e(i.closeHtml),v={intervalId:null,hideEta:null,maxHideTime:null},C={toastId:o,state:"visible",startTime:new Date,options:i,map:n};return n.iconClass&&l.addClass(i.toastClass).addClass(a),function(){if(n.title){var e=n.title;i.escapeHtml&&(e=w(n.title)),c.append(e).addClass(i.titleClass),l.append(c)}}(),function(){if(n.message){var e=n.message;i.escapeHtml&&(e=w(n.message)),u.append(e).addClass(i.messageClass),l.append(u)}}(),i.closeButton&&(h.addClass(i.closeClass).attr("role","button"),l.prepend(h)),i.progressBar&&(m.addClass(i.progressClass),l.prepend(m)),i.rtl&&l.addClass("rtl"),i.newestOnTop?t.prepend(l):t.append(l),function(){var e="";switch(n.iconClass){case"toast-success":case"toast-info":e="polite";break;default:e="assertive"}l.attr("aria-live",e)}(),l.hide(),l[i.showMethod]({duration:i.showDuration,easing:i.showEasing,complete:i.onShown}),i.timeOut>0&&(r=setTimeout(T,i.timeOut),v.maxHideTime=parseFloat(i.timeOut),v.hideEta=(new Date).getTime()+v.maxHideTime,i.progressBar&&(v.intervalId=setInterval(D,10))),function(){i.closeOnHover&&l.hover(b,O);!i.onclick&&i.tapToDismiss&&l.click(T);i.closeButton&&h&&h.click((function(e){e.stopPropagation?e.stopPropagation():void 0!==e.cancelBubble&&!0!==e.cancelBubble&&(e.cancelBubble=!0),i.onCloseClick&&i.onCloseClick(e),T(!0)}));i.onclick&&l.click((function(e){i.onclick(e),T()}))}(),p(C),i.debug&&console&&console.log(C),l}function w(e){return null==e&&(e=""),e.replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&#39;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}function T(t){var n=t&&!1!==i.closeMethod?i.closeMethod:i.hideMethod,s=t&&!1!==i.closeDuration?i.closeDuration:i.hideDuration,o=t&&!1!==i.closeEasing?i.closeEasing:i.hideEasing;if(!e(":focus",l).length||t)return clearTimeout(v.intervalId),l[n]({duration:s,easing:o,complete:function(){g(l),clearTimeout(r),i.onHidden&&"hidden"!==C.state&&i.onHidden(),C.state="hidden",C.endTime=new Date,p(C)}})}function O(){(i.timeOut>0||i.extendedTimeOut>0)&&(r=setTimeout(T,i.extendedTimeOut),v.maxHideTime=parseFloat(i.extendedTimeOut),v.hideEta=(new Date).getTime()+v.maxHideTime)}function b(){clearTimeout(r),v.hideEta=0,l.stop(!0,!0)[i.showMethod]({duration:i.showDuration,easing:i.showEasing})}function D(){var e=(v.hideEta-(new Date).getTime())/v.maxHideTime*100;m.width(e+"%")}}function f(){return e.extend({},{tapToDismiss:!0,toastClass:"toast",containerId:"toast-container",debug:!1,showMethod:"fadeIn",showDuration:300,showEasing:"swing",onShown:void 0,hideMethod:"fadeOut",hideDuration:1e3,hideEasing:"swing",onHidden:void 0,closeMethod:!1,closeDuration:!1,closeEasing:!1,closeOnHover:!0,extendedTimeOut:1e3,iconClasses:{error:"toast-error",info:"toast-info",success:"toast-success",warning:"toast-warning"},iconClass:"toast-info",positionClass:"toast-top-right",timeOut:5e3,titleClass:"toast-title",messageClass:"toast-message",escapeHtml:!1,target:"body",closeHtml:'<button type="button">&times;</button>',closeClass:"toast-close-button",newestOnTop:!0,preventDuplicates:!1,progressBar:!1,progressClass:"toast-progress",rtl:!1},c.options)}function g(e){t||(t=d()),e.is(":visible")||(e.remove(),e=null,0===t.children().length&&(t.remove(),s=void 0))}}()}))}("function"==typeof define&&define.amd?define:function(e,t){"undefined"!=typeof module&&module.exports?module.exports=t(require("jquery")):window.toastr=t(window.jQuery)});
//# sourceMappingURL=/sm/af0065d4aac85e5a5997fd40f32d1abbf1835a501452c048fdcef751069cbb58.map