<?php

namespace App\Http\Controllers;

use App\Models\movements;
use Illuminate\Http\Request;

class MovementsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(movements $movements)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(movements $movements)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, movements $movements)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(movements $movements)
    {
        //
    }
}
