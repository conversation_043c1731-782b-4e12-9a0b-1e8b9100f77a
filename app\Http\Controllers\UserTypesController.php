<?php

namespace App\Http\Controllers;

use App\Models\user_types;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class UserTypesController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(user_types $user_types)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(user_types $user_types)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, user_types $user_types)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(user_types $user_types)
    {
        //
    }
}
