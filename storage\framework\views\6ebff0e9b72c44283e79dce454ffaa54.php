<?php echo $__env->make('navigation/header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <body class="sb-nav-fixed">
       <?php echo $__env->make('navigation/navigation', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <div id="layoutSidenav">
            <div id="layoutSidenav_nav">
                <?php echo $__env->make('navigation/sidebar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            </div>
            <div id="layoutSidenav_content">
                <main>
                    <div class="container-fluid px-6">
                        <h1></h1>
                        <div class="card ">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="card-header">
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <i class="fas fa-user"></i>
                                                Information
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div class="col-md-12">
                                            <table class = "table  table-bordered table-stripped">
                                                <thead class="table-primary">
                                                    <tr>
                                                        <th>NAME</th>
                                                        <th><?php echo e($profile[0]->fullname); ?></th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <th>POSITION</th>
                                                        <td><?php echo e($profile[0]->position); ?></td>
                                                    </tr>
                                                    <tr>
                                                        <th>DEPARTMENT</th>
                                                        <td><?php echo e($profile[0]->department_name); ?></td>
                                                    </tr>
                                                    <tr>
                                                        <th>EMAIL</th>
                                                        <td><?php echo e($profile[0]->email); ?></td>
                                                    </tr>
                                                    <tr>
                                                        <th>CONTACT NUMBER</th>
                                                        <td><?php echo e($profile[0]->contact_number); ?></td>
                                                    </tr>
                                                    <tr>
                                                        <th>ROLE</th>
                                                        <td><?php echo e($profile[0]->role == 1 ? "ADMIN" : "PURCHASER"); ?></td>
                                                    </tr>
                                                    <tr>
                                                        <th>USERNAME</th>
                                                        <td><?php echo e($profile[0]->username); ?></td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card-header">
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <i class="fas fa-user"></i>
                                               Login Credentials
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-body ">
                                        <div class  ="col-md-12">
                                            <?php if(Session::get('success')): ?>
                                                <div class="alert alert-success">
                                                    <?php echo e(Session::get('success')); ?>

                                                </div>
                                            <?php endif; ?>
                                            <?php if(Session::get('PasswordError')): ?>
                                                <div class="alert alert-danger">
                                                    <?php echo e(Session::get('PasswordError')); ?>

                                                </div>
                                            <?php endif; ?>
                                            <?php if(Session::get('NPasswordError')): ?>
                                                <div class="alert alert-danger">
                                                    <?php echo e(Session::get('NPasswordError')); ?>

                                                </div>
                                            <?php endif; ?>
                                            <form method = "post" action="<?php echo e(route('users.admin_changePass')); ?>">
                                                <?php echo csrf_field(); ?>
                                                <div class="form-group">
                                                    <label for="username">Username</label>
                                                    <input autocomplete="off" onkeyup="$(this).removeClass('is-invalid'); $('#username-msg').html('');" type="text" name = "username"  value = "<?php echo e(old('username')); ?>" class="form-control" id="username" placeholder="Enter your username">
                                                    <?php $__errorArgs = ['username'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <span class="badge badge-danger"><?php echo e($message); ?></span>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                                <div class="form-group">
                                                    <label for="password">Current Password</label>
                                                    <input autocomplete="off" onkeyup="$(this).removeClass('is-invalid'); $('#currpwd-msg').html('');" type="password" name = "currpwd" class="form-control" id="currpwd" placeholder="Enter your current password">
                                                    <?php $__errorArgs = ['currpwd'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <span class="badge badge-danger"><?php echo e($message); ?></span>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                                <div class="form-group">
                                                    <label for="password">New Password</label>
                                                    <input autocomplete="off" onkeyup="$(this).removeClass('is-invalid'); $('#newpwd-msg').html('');" type="password" name = "newpwd" class="form-control" id="newpwd" placeholder="Enter your new password">
                                                    <?php $__errorArgs = ['newpwd'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <span class="badge badge-danger"><?php echo e($message); ?></span>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                                <div class="form-group">
                                                    <label for="password">Confirm Password</label>
                                                    <input autocomplete="off" onkeyup="$(this).removeClass('is-invalid'); $('#conpwd-msg').html('');" type="password" name = "conpwd" class="form-control" id="conpwd" placeholder="Enter your confirm password">
                                                    <?php $__errorArgs = ['conpwd'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <span class="badge badge-danger"><?php echo e($message); ?></span>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                                <p></p><p></p>
                                                <div class="form-group">
                                                   <button type="submit" class = "btn btn-success btn-block"><i class = "fas fa-save"></i>&nbsp;Save</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6" style = "text-align:center">
                                    <div class="img-responsive">
                                        <img src="<?php echo e(asset('admintemplate/assets/img/shs-logo.png')); ?>" alt=""  style = "width: 350px; height: 350px;">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </main>
            </div>
        </div>

    <?php echo $__env->make('navigation/footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <script  type="text/javascript">
        document.title = "LSHS Profile";
        $(document).ready(function(){
            $("#s_profiles").addClass('active');
            $.ajaxSetup({
                headers: {
                    'X-CSRF-Token':$("input[name=_token").val()
                }
            })
        });
    </script><?php /**PATH C:\Users\<USER>\Desktop\lugaitims\resources\views/pages/adminprofile.blade.php ENDPATH**/ ?>